from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.db import models
from .forms import UnitForm, TenantForm, LeaseForm, InvoiceForm, PaymentForm
from rentals.models import Unit, Tenant, Lease, Invoice, Payment


# Unit CRUD Views
@login_required
def unit_create(request, org_slug):
    if request.method == 'POST':
        form = UnitForm(request.POST, organization=request.org)
        if form.is_valid():
            unit = form.save(commit=False)
            unit.organization = request.org
            unit.save()
            messages.success(request, 'Unit created successfully!')
            return redirect('unit_list', org_slug=org_slug)
    else:
        form = UnitForm(organization=request.org)
    return render(request, 'rentals/unit_form.html', {'form': form, 'title': 'Add Unit'})


@login_required
def unit_edit(request, org_slug, pk):
    unit = get_object_or_404(Unit, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = UnitForm(request.POST, instance=unit, organization=request.org)
        if form.is_valid():
            form.save()
            messages.success(request, 'Unit updated successfully!')
            return redirect('unit_list', org_slug=org_slug)
    else:
        form = UnitForm(instance=unit, organization=request.org)
    return render(request, 'rentals/unit_form.html', {'form': form, 'title': 'Edit Unit'})


@login_required
def unit_delete(request, org_slug, pk):
    unit = get_object_or_404(Unit, pk=pk, organization=request.org)
    if request.method == 'POST':
        unit.delete()
        messages.success(request, 'Unit deleted successfully!')
        return redirect('unit_list', org_slug=org_slug)
    return render(request, 'rentals/unit_confirm_delete.html', {'object': unit})


# Tenant CRUD Views
@login_required
def tenant_create(request, org_slug):
    if request.method == 'POST':
        form = TenantForm(request.POST)
        if form.is_valid():
            tenant = form.save(commit=False)
            tenant.organization = request.org
            tenant.save()
            messages.success(request, 'Tenant created successfully!')
            return redirect('tenant_list', org_slug=org_slug)
    else:
        form = TenantForm()
    return render(request, 'rentals/tenant_form.html', {'form': form, 'title': 'Add Tenant'})


@login_required
def tenant_edit(request, org_slug, pk):
    tenant = get_object_or_404(Tenant, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = TenantForm(request.POST, instance=tenant)
        if form.is_valid():
            form.save()
            messages.success(request, 'Tenant updated successfully!')
            return redirect('tenant_list', org_slug=org_slug)
    else:
        form = TenantForm(instance=tenant)
    return render(request, 'rentals/tenant_form.html', {'form': form, 'title': 'Edit Tenant'})


@login_required
def tenant_delete(request, org_slug, pk):
    tenant = get_object_or_404(Tenant, pk=pk, organization=request.org)
    if request.method == 'POST':
        tenant.delete()
        messages.success(request, 'Tenant deleted successfully!')
        return redirect('tenant_list', org_slug=org_slug)
    return render(request, 'rentals/tenant_confirm_delete.html', {'object': tenant})


# Lease CRUD Views
@login_required
def lease_create(request, org_slug):
    if request.method == 'POST':
        form = LeaseForm(request.POST, organization=request.org)
        if form.is_valid():
            lease = form.save(commit=False)
            lease.organization = request.org
            lease.save()
            messages.success(request, 'Lease created successfully!')
            return redirect('lease_list', org_slug=org_slug)
    else:
        form = LeaseForm(organization=request.org)
    return render(request, 'rentals/lease_form.html', {'form': form, 'title': 'Add Lease'})


@login_required
def lease_edit(request, org_slug, pk):
    lease = get_object_or_404(Lease, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = LeaseForm(request.POST, instance=lease, organization=request.org)
        if form.is_valid():
            form.save()
            messages.success(request, 'Lease updated successfully!')
            return redirect('lease_list', org_slug=org_slug)
    else:
        form = LeaseForm(instance=lease, organization=request.org)
    return render(request, 'rentals/lease_form.html', {'form': form, 'title': 'Edit Lease'})


@login_required
def lease_delete(request, org_slug, pk):
    lease = get_object_or_404(Lease, pk=pk, organization=request.org)
    if request.method == 'POST':
        lease.delete()
        messages.success(request, 'Lease deleted successfully!')
        return redirect('lease_list', org_slug=org_slug)
    return render(request, 'rentals/lease_confirm_delete.html', {'object': lease})


# Invoice CRUD Views
@login_required
def invoice_create(request, org_slug):
    if request.method == 'POST':
        form = InvoiceForm(request.POST, organization=request.org)
        if form.is_valid():
            invoice = form.save(commit=False)
            invoice.organization = request.org
            invoice.save()
            messages.success(request, 'Invoice created successfully!')
            return redirect('invoice_list', org_slug=org_slug)
    else:
        form = InvoiceForm(organization=request.org)
    return render(request, 'billing/invoice_form.html', {'form': form, 'title': 'Add Invoice'})


@login_required
def invoice_edit(request, org_slug, pk):
    invoice = get_object_or_404(Invoice, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = InvoiceForm(request.POST, instance=invoice, organization=request.org)
        if form.is_valid():
            form.save()
            messages.success(request, 'Invoice updated successfully!')
            return redirect('invoice_list', org_slug=org_slug)
    else:
        form = InvoiceForm(instance=invoice, organization=request.org)
    return render(request, 'billing/invoice_form.html', {'form': form, 'title': 'Edit Invoice'})


# Payment CRUD Views
@login_required
def payment_create(request, org_slug):
    if request.method == 'POST':
        form = PaymentForm(request.POST, organization=request.org)
        if form.is_valid():
            payment = form.save(commit=False)
            payment.organization = request.org
            payment.save()
            
            # Update invoice payment status
            invoice = payment.invoice
            total_payments = Payment.objects.filter(invoice=invoice).aggregate(
                total=models.Sum('amount'))['total'] or 0
            invoice.amount_paid = total_payments
            invoice.is_paid = invoice.amount_paid >= invoice.amount_due
            invoice.save()
            
            messages.success(request, 'Payment recorded successfully!')
            return redirect('payment_list', org_slug=org_slug)
    else:
        form = PaymentForm(organization=request.org)
    return render(request, 'billing/payment_form.html', {'form': form, 'title': 'Record Payment'})
