from django import forms
from rentals.models import Property, Unit, Tenant, Lease, Invoice, Payment, MaintenanceRequest

class LoginForm(forms.Form):
    username = forms.CharField()
    password = forms.CharField(widget=forms.PasswordInput)


class PropertyForm(forms.ModelForm):
    class Meta:
        model = Property
        fields = ['name', 'address']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name.strip()) < 2:
            raise forms.ValidationError('Property name must be at least 2 characters long.')
        return name.strip()


class UnitForm(forms.ModelForm):
    class Meta:
        model = Unit
        fields = ['property', 'code', 'bedrooms', 'bathrooms', 'is_active']
        widgets = {
            'property': forms.Select(attrs={'class': 'form-select'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'bedrooms': forms.NumberInput(attrs={'class': 'form-control'}),
            'bathrooms': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['property'].queryset = Property.objects.filter(organization=organization)

    def clean_code(self):
        code = self.cleaned_data['code']
        if len(code.strip()) < 1:
            raise forms.ValidationError('Unit code is required.')
        return code.strip().upper()

    def clean_bedrooms(self):
        bedrooms = self.cleaned_data['bedrooms']
        if bedrooms < 0 or bedrooms > 10:
            raise forms.ValidationError('Bedrooms must be between 0 and 10.')
        return bedrooms

    def clean_bathrooms(self):
        bathrooms = self.cleaned_data['bathrooms']
        if bathrooms < 0 or bathrooms > 10:
            raise forms.ValidationError('Bathrooms must be between 0 and 10.')
        return bathrooms


class TenantForm(forms.ModelForm):
    class Meta:
        model = Tenant
        fields = ['first_name', 'last_name', 'email', 'phone', 'id_number', 'notes']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'id_number': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_email(self):
        email = self.cleaned_data['email']
        # Check if email is unique within the organization (if we have access to it)
        return email.lower()

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # Remove non-digit characters for validation
            digits_only = ''.join(filter(str.isdigit, phone))
            if len(digits_only) < 10:
                raise forms.ValidationError('Phone number must have at least 10 digits.')
        return phone


class LeaseForm(forms.ModelForm):
    class Meta:
        model = Lease
        fields = ['unit', 'tenant', 'start_date', 'end_date', 'rent_amount', 'deposit_amount', 'billing_day', 'status']
        widgets = {
            'unit': forms.Select(attrs={'class': 'form-select'}),
            'tenant': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'rent_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'deposit_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'billing_day': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '31'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['unit'].queryset = Unit.objects.filter(organization=organization)
            self.fields['tenant'].queryset = Tenant.objects.filter(organization=organization)

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        rent_amount = cleaned_data.get('rent_amount')
        deposit_amount = cleaned_data.get('deposit_amount')
        billing_day = cleaned_data.get('billing_day')

        # Validate date range
        if start_date and end_date and end_date <= start_date:
            raise forms.ValidationError('End date must be after start date.')

        # Validate rent amount
        if rent_amount and rent_amount <= 0:
            raise forms.ValidationError('Rent amount must be greater than 0.')

        # Validate deposit amount
        if deposit_amount and deposit_amount < 0:
            raise forms.ValidationError('Deposit amount cannot be negative.')

        # Validate billing day
        if billing_day and (billing_day < 1 or billing_day > 31):
            raise forms.ValidationError('Billing day must be between 1 and 31.')

        return cleaned_data


class InvoiceForm(forms.ModelForm):
    class Meta:
        model = Invoice
        fields = ['lease', 'number', 'issue_date', 'due_date', 'amount_due']
        widgets = {
            'lease': forms.Select(attrs={'class': 'form-select'}),
            'number': forms.TextInput(attrs={'class': 'form-control'}),
            'issue_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'amount_due': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['lease'].queryset = Lease.objects.filter(organization=organization)

    def clean(self):
        cleaned_data = super().clean()
        issue_date = cleaned_data.get('issue_date')
        due_date = cleaned_data.get('due_date')
        amount_due = cleaned_data.get('amount_due')

        # Validate date range
        if issue_date and due_date and due_date < issue_date:
            raise forms.ValidationError('Due date cannot be before issue date.')

        # Validate amount
        if amount_due and amount_due <= 0:
            raise forms.ValidationError('Amount due must be greater than 0.')

        return cleaned_data


class PaymentForm(forms.ModelForm):
    class Meta:
        model = Payment
        fields = ['invoice', 'date', 'reference', 'method', 'amount']
        widgets = {
            'invoice': forms.Select(attrs={'class': 'form-select'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reference': forms.TextInput(attrs={'class': 'form-control'}),
            'method': forms.TextInput(attrs={'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['invoice'].queryset = Invoice.objects.filter(organization=organization, is_paid=False)

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError('Payment amount must be greater than 0.')

        # Check if payment amount doesn't exceed remaining balance
        invoice = self.cleaned_data.get('invoice')
        if invoice:
            remaining_balance = invoice.amount_due - invoice.amount_paid
            if amount > remaining_balance:
                raise forms.ValidationError(
                    f'Payment amount cannot exceed remaining balance of ${remaining_balance}.'
                )

        return amount