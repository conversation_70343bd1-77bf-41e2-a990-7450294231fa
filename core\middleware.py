from django.http import Http404
from .models import Organization


class OrganizationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):   # ✅ correctly indented inside the class
        request.org = None
        match = getattr(request, 'resolver_match', None)
        if match and 'org_slug' in match.kwargs:
            slug = match.kwargs.get('org_slug')
            try:
                request.org = Organization.objects.get(slug=slug)
            except Organization.DoesNotExist:
                raise Http404('Organization not found')
        return self.get_response(request)
