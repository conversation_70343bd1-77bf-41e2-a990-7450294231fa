from django.urls import path, include
from . import views, crud_views


urlpatterns = [
    path('<str:org_slug>/', include([
        path('', views.dashboard, name='dashboard'),
        path('auth/login/', views.login_view, name='login'),
        path('auth/logout/', views.logout_view, name='logout'),

        path('rentals/', include('rentals.urls')),

        # Property CRUD
        path('properties/add/', views.property_create, name='property_create'),
        path('properties/<int:pk>/edit/', views.property_edit, name='property_edit'),
        path('properties/<int:pk>/delete/', views.property_delete, name='property_delete'),

        # Unit CRUD
        path('units/add/', crud_views.unit_create, name='unit_create'),
        path('units/<int:pk>/edit/', crud_views.unit_edit, name='unit_edit'),
        path('units/<int:pk>/delete/', crud_views.unit_delete, name='unit_delete'),

        # Tenant CRUD
        path('tenants/add/', crud_views.tenant_create, name='tenant_create'),
        path('tenants/<int:pk>/edit/', crud_views.tenant_edit, name='tenant_edit'),
        path('tenants/<int:pk>/delete/', crud_views.tenant_delete, name='tenant_delete'),

        # Lease CRUD
        path('leases/add/', crud_views.lease_create, name='lease_create'),
        path('leases/<int:pk>/edit/', crud_views.lease_edit, name='lease_edit'),
        path('leases/<int:pk>/delete/', crud_views.lease_delete, name='lease_delete'),

        # Invoice CRUD
        path('invoices/add/', crud_views.invoice_create, name='invoice_create'),
        path('invoices/<int:pk>/edit/', crud_views.invoice_edit, name='invoice_edit'),

        # Payment CRUD
        path('payments/add/', crud_views.payment_create, name='payment_create'),
    ])),
]