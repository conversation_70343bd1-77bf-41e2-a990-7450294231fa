from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from .forms import LoginForm, PropertyForm, UnitForm, TenantForm, LeaseForm, InvoiceForm, PaymentForm
from rentals.models import Invoice, Lease, Unit, Property, Tenant, Payment


def login_view(request, org_slug):
    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            user = authenticate(
                request,
                username=form.cleaned_data['username'],
                password=form.cleaned_data['password'],
            )
            if user:
                login(request, user)
                messages.success(request, f'Welcome back, {user.username}!')
                return redirect('dashboard', org_slug=org_slug)
            else:
                messages.error(request, 'Invalid username or password.')
    else:
        form = LoginForm()

    return render(request, 'auth/auth-login.html', {'form': form, 'org_slug': org_slug})


def logout_view(request, org_slug):
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('login', org_slug=org_slug)


@login_required
def dashboard(request, org_slug):
    org = request.org
    kpis = {
        'properties_total': Property.objects.filter(organization=org).count(),
        'units_total': Unit.objects.filter(organization=org).count(),
        'leases_active': Lease.objects.filter(organization=org, status='ACTIVE').count(),
        'invoices_unpaid': Invoice.objects.filter(organization=org, is_paid=False).count(),
    }
    latest_invoices = Invoice.objects.filter(organization=org)[:10]
    return render(request, 'dashboard/index.html', {'kpis': kpis, 'latest_invoices': latest_invoices})


def debug_view(request, org_slug):
    """Debug view to check context variables"""
    return render(request, 'debug_context.html')


# Property CRUD Views
@login_required
def property_create(request, org_slug):
    if request.method == 'POST':
        form = PropertyForm(request.POST)
        if form.is_valid():
            property_obj = form.save(commit=False)
            property_obj.organization = request.org
            property_obj.save()
            messages.success(request, 'Property created successfully!')
            return redirect('property_list', org_slug=org_slug)
    else:
        form = PropertyForm()
    return render(request, 'rentals/property_form.html', {'form': form, 'title': 'Add Property'})


@login_required
def property_edit(request, org_slug, pk):
    property_obj = get_object_or_404(Property, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = PropertyForm(request.POST, instance=property_obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'Property updated successfully!')
            return redirect('property_list', org_slug=org_slug)
    else:
        form = PropertyForm(instance=property_obj)
    return render(request, 'rentals/property_form.html', {'form': form, 'title': 'Edit Property'})


@login_required
def property_delete(request, org_slug, pk):
    property_obj = get_object_or_404(Property, pk=pk, organization=request.org)
    if request.method == 'POST':
        property_obj.delete()
        messages.success(request, 'Property deleted successfully!')
        return redirect('property_list', org_slug=org_slug)
    return render(request, 'rentals/property_confirm_delete.html', {'object': property_obj})