from django.contrib import admin
from .models import Property, Unit, Tenant, Lease, Invoice, Payment, MaintenanceRequest


@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'address')


@admin.register(Unit)
class UnitAdmin(admin.ModelAdmin):
    list_display = ('code', 'property', 'organization', 'bedrooms', 'bathrooms', 'is_active')


@admin.register(Tenant)
class TenantAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'email', 'organization')


@admin.register(Lease)
class LeaseAdmin(admin.ModelAdmin):
    list_display = ('unit', 'tenant', 'start_date', 'end_date', 'rent_amount', 'organization')


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('number', 'lease', 'issue_date', 'due_date', 'amount_due', 'amount_paid', 'is_paid', 'organization')


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('invoice', 'date', 'reference', 'method', 'amount', 'organization')


@admin.register(MaintenanceRequest)
class MaintenanceRequestAdmin(admin.ModelAdmin):
    list_display = ('title', 'unit', 'priority', 'status', 'organization', 'created_at')