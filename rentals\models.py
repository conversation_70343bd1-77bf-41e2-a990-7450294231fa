from django.db import models
from django.contrib.auth.models import User
from core.models import OrgScopedModel
from decimal import Decimal


class Property(OrgScopedModel):
    name = models.CharField(max_length=150)
    address = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class Unit(OrgScopedModel):
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    code = models.CharField(max_length=50)
    bedrooms = models.PositiveIntegerField()
    bathrooms = models.PositiveIntegerField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.property.name} - {self.code}"


class Tenant(OrgScopedModel):
    first_name = models.Char<PERSON><PERSON>(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True, null=True)
    id_number = models.CharField(max_length=20, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name}"


class Lease(OrgScopedModel):
    STATUS = [('ACTIVE','Active'),
              ('ENDED','Ended'),
              ('PENDING','Pending')
              ]
    unit = models.ForeignKey(Unit, on_delete=models.PROTECT)
    tenant = models.ForeignKey(Tenant, on_delete=models.PROTECT)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    rent_amount = models.DecimalField(max_digits=10, decimal_places=2)
    from decimal import Decimal
    deposit_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    billing_day = models.PositiveSmallIntegerField(default=1)
    status = models.CharField(max_length=20, choices=STATUS, default='ACTIVE')

    class Meta:
        unique_together = ('organization', 'unit', 'tenant', 'start_date')

class Invoice(OrgScopedModel):
    lease = models.ForeignKey(Lease, on_delete=models.CASCADE, related_name='invoices')
    number = models.CharField(max_length=30)
    issue_date = models.DateField()
    due_date = models.DateField()
    amount_due = models.DecimalField(max_digits=10, decimal_places=2)
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    is_paid = models.BooleanField(default=False)


    class Meta:
        unique_together = ('organization', 'number')
        ordering = ['-issue_date']


    @property
    def balance(self):
        return self.amount_due - self.amount_paid
    
class Payment(OrgScopedModel):
    invoice = models.ForeignKey(Invoice, on_delete=models.PROTECT, related_name='payments')
    date = models.DateField()
    reference = models.CharField(max_length=50, blank=True)
    method = models.CharField(max_length=30, default='Cash')
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        ordering = ['-date']
        unique_together = ('organization', 'invoice', 'reference')

class MaintenanceRequest(OrgScopedModel):
    PRIORITY = [('LOW','Low'), ('MEDIUM','Medium'), ('HIGH','High')]
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    title = models.CharField(max_length=150)
    description = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY, default='MEDIUM')
    status = models.CharField(max_length=20, default='Open')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)


    def __str__(self):
        return self.title