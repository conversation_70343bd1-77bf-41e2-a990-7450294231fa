from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404
from .models import Property, Unit, Tenant, Lease, Invoice, Payment


@login_required
def property_list(request, org_slug):
    qs = Property.objects.filter(organization=request.org)
    return render(request, 'rentals/property_list.html', {'items': qs})


@login_required
def unit_list(request, org_slug):
    qs = Unit.objects.filter(organization=request.org).select_related('property')
    return render(request, 'rentals/unit_list.html', {'items': qs})


@login_required
def tenant_list(request, org_slug):
    qs = Tenant.objects.filter(organization=request.org)
    return render(request, 'rentals/tenant_list.html', {'items': qs})


@login_required
def lease_list(request, org_slug):
    qs = Lease.objects.filter(organization=request.org).select_related('unit', 'tenant')
    return render(request, 'rentals/lease_list.html', {'items': qs})


@login_required
def invoice_list(request, org_slug):
    qs = Invoice.objects.filter(organization=request.org)
    return render(request, 'billing/invoice_list.html', {'items': qs})


@login_required
def invoice_detail(request, org_slug, pk):
    obj = get_object_or_404(Invoice, pk=pk, organization=request.org)
    return render(request, 'billing/invoice_detail.html', {'invoice': obj})


@login_required
def payment_list(request, org_slug):
    qs = Payment.objects.filter(organization=request.org).select_related('invoice')
    return render(request, 'billing/payment_list.html', {'items': qs})
