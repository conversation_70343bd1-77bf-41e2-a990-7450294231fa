from django.contrib import admin
from django.urls import path, include
from django.shortcuts import render, redirect
from django.contrib.auth import logout
from core.models import Organization, Membership

def root_login_view(request):
    """Root login view - main entry point"""
    if request.user.is_authenticated:
        # User is already logged in, redirect to their dashboard
        return redirect('dashboard_redirect')

    # Show login form
    if request.method == 'POST':
        from django.contrib.auth import authenticate, login
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user:
            login(request, user)
            return redirect('dashboard_redirect')
        else:
            return render(request, 'auth/auth-login.html', {'error': 'Invalid credentials'})

    return render(request, 'auth/auth-login.html')


def dashboard_redirect_view(request):
    """Redirect authenticated users to their appropriate dashboard"""
    if not request.user.is_authenticated:
        return redirect('root_login')

    # Get user's organization memberships
    memberships = Membership.objects.filter(user=request.user).select_related('organization')

    if memberships.count() == 1:
        # User belongs to one organization - redirect directly
        membership = memberships.first()
        if membership and membership.organization:
            return redirect(f'/{membership.organization.slug}/')
    elif memberships.count() > 1:
        # User belongs to multiple organizations - show selection
        return render(request, 'dashboard/org_selection.html', {'memberships': memberships})

    # Handle users with no memberships - prioritize superuser access
    if request.user.is_superuser:
        # Superuser can access any organization
        organizations = Organization.objects.all()
        if organizations.exists():
            return render(request, 'dashboard/admin_org_selection.html', {'organizations': organizations})
        else:
            return render(request, 'root_welcome.html')
    else:
        # Regular user with no memberships - show access denied
        return render(request, 'no_access.html')


def root_logout_view(request):
    """Global logout view"""
    logout(request)
    return redirect('auth-login')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', root_login_view, name='auth-login'),
    path('dashboard/', dashboard_redirect_view, name='dashboard_redirect'),
    path('logout/', root_logout_view, name='root_logout'),
    path('', include('core.urls')),
]
