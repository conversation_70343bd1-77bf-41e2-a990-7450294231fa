{% extends 'base.html' %}
{% block title %}Invoice {{ invoice.number }}{% endblock %}
{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <div class="h5 mb-0">Invoice {{ invoice.number }}
                {% if invoice.is_paid %}<span class="badge text-bg-success">Paid</span>{% else %}<span class="badge text-bg-warning">Unpaid</span>{% endif %}
            </div>
            <div class="text-muted small">Lease: {{ invoice.lease.tenant }} – Unit {{ invoice.lease.unit.code }}</div>
            </div>
            <div>
                {% if not invoice.is_paid %}
                    <button class="btn btn-sm btn-success disabled" title="Placeholder">Record Payment</button>
                {% endif %}
            </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between"><span>Issue Date</span><strong>{{ invoice.issue_date }}</strong></li>
                        <li class="list-group-item d-flex justify-content-between"><span>Due Date</span><strong>{{ invoice.due_date }}</strong></li>
                        <li class="list-group-item d-flex justify-content-between"><span>Amount Due</span><strong>{{ invoice.amount_due }}</strong></li>
                        <li class="list-group-item d-flex justify-content-between"><span>Amount Paid</span><strong>{{ invoice.amount_paid }}</strong></li>
                        <li class="list-group-item d-flex justify-content-between"><span>Balance</span><strong>{{ invoice.balance }}</strong></li>
                        </ul>
                    </div>
                <div class="col-md-6">
            <h6>Payments</h6>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Ref</th>
                            <th>Method</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for p in invoice.payments.all %}
                    <tr>
                        <td>{{ p.date }}</td>
                        <td>{{ p.reference }}</td>
                        <td>{{ p.method }}</td>
                        <td>{{ p.amount }}</td>
                    </tr>
                        {% empty %}
                    <tr>
                        <td colspan="4">No payments yet.</td>
                    </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}