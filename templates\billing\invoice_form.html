{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">{{ title }}</h1>
    <a href="{% org_url 'invoice_list' %}" class="btn btn-secondary">Back to Invoices</a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.lease.id_for_label }}" class="form-label">Lease</label>
                {{ form.lease }}
                {% if form.lease.errors %}
                    <div class="text-danger">{{ form.lease.errors }}</div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.number.id_for_label }}" class="form-label">Invoice Number</label>
                {{ form.number }}
                {% if form.number.errors %}
                    <div class="text-danger">{{ form.number.errors }}</div>
                {% endif %}
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.issue_date.id_for_label }}" class="form-label">Issue Date</label>
                        {{ form.issue_date }}
                        {% if form.issue_date.errors %}
                            <div class="text-danger">{{ form.issue_date.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.due_date.id_for_label }}" class="form-label">Due Date</label>
                        {{ form.due_date }}
                        {% if form.due_date.errors %}
                            <div class="text-danger">{{ form.due_date.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="{{ form.amount_due.id_for_label }}" class="form-label">Amount Due</label>
                {{ form.amount_due }}
                {% if form.amount_due.errors %}
                    <div class="text-danger">{{ form.amount_due.errors }}</div>
                {% endif %}
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Save Invoice</button>
                <a href="{% org_url 'invoice_list' %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
