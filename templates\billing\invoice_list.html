{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Invoices{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">Invoices</h1>
    <div class="btn-group">
        <a href="{% org_url 'invoice_create' %}" class="btn btn-sm btn-primary">Add Invoice</a>
        <a href="{% org_url 'payment_create' %}" class="btn btn-sm btn-success">Record Payment</a>
    </div>
</div>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>Tenant</th>
                <th>Unit</th>
                <th>Issue</th>
                <th>Due</th>
                <th>Amount</th>
                <th>Paid</th>
                <th>Balance</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for inv in items %}
        <tr>
            <td>
                <a href="{% org_url 'invoice_detail' pk=inv.pk %}">{{ inv.number }}</a>
            </td>
            <td>{{ inv.lease.tenant }}</td>
            <td>{{ inv.lease.unit.code }}</td>
            <td>{{ inv.issue_date }}</td>
            <td>{{ inv.due_date }}</td>
            <td>{{ inv.amount_due }}</td>
            <td>{{ inv.amount_paid }}</td>
            <td>{{ inv.balance }}</td>
            <td>{% if inv.is_paid %}<span class="badge bg-success">Paid</span>{% else %}<span class="badge bg-warning">Unpaid</span>{% endif %}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="{% org_url 'invoice_detail' pk=inv.pk %}" class="btn btn-outline-secondary">View</a>
                    {% if not inv.is_paid %}
                        <a href="{% org_url 'invoice_edit' pk=inv.pk %}" class="btn btn-outline-primary">Edit</a>
                    {% endif %}
                </div>
            </td>
        </tr>
            {% empty %}
        <tr>
            <td colspan="10" class="text-center">No invoices yet.</td>
        </tr>
            {% endfor %}
        </tbody>
    </table>
{% endblock %}