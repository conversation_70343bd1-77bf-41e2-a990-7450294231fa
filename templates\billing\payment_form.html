{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">{{ title }}</h1>
    <a href="{% org_url 'payment_list' %}" class="btn btn-secondary">Back to Payments</a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.invoice.id_for_label }}" class="form-label">Invoice</label>
                {{ form.invoice }}
                {% if form.invoice.errors %}
                    <div class="text-danger">{{ form.invoice.errors }}</div>
                {% endif %}
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.date.id_for_label }}" class="form-label">Payment Date</label>
                        {{ form.date }}
                        {% if form.date.errors %}
                            <div class="text-danger">{{ form.date.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.amount.id_for_label }}" class="form-label">Amount</label>
                        {{ form.amount }}
                        {% if form.amount.errors %}
                            <div class="text-danger">{{ form.amount.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.method.id_for_label }}" class="form-label">Payment Method</label>
                        {{ form.method }}
                        {% if form.method.errors %}
                            <div class="text-danger">{{ form.method.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.reference.id_for_label }}" class="form-label">Reference</label>
                        {{ form.reference }}
                        {% if form.reference.errors %}
                            <div class="text-danger">{{ form.reference.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Record Payment</button>
                <a href="{% org_url 'payment_list' %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
