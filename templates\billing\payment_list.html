{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Payments{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">Payments</h1>
    <a href="{% org_url 'payment_create' %}" class="btn btn-sm btn-primary">Record Payment</a>
</div>

<div class="card">
    <div class="card-body p-0">
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Invoice</th>
                    <th>Tenant</th>
                    <th>Amount</th>
                    <th>Method</th>
                    <th>Reference</th>
                </tr>
            </thead>
            <tbody>
                {% for payment in items %}
                <tr>
                    <td>{{ payment.date }}</td>
                    <td><a href="{% org_url 'invoice_detail' pk=payment.invoice.pk %}">{{ payment.invoice.number }}</a></td>
                    <td>{{ payment.invoice.lease.tenant }}</td>
                    <td>${{ payment.amount }}</td>
                    <td>{{ payment.method }}</td>
                    <td>{{ payment.reference|default:'—' }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="text-center">No payments yet.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
