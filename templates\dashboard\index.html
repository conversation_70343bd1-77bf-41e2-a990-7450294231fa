{% extends 'base.html' %}
{% load static %}
{% load org_urls %}
{% block title %}Dashboard{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}

<body>
  <div class="loader"></div>
  <div id="app">
    <div class="main-wrapper main-wrapper-1">
      <!-- Main Content -->
      <div class="main-content">
        <section class="section">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
              <i class="fas fa-tachometer-alt"></i>
              Dashboard
            </h1>
            <div class="text-muted">
              <i class="fas fa-calendar"></i>
              {{ "now"|date:"F d, Y" }}
            </div>
          </div>

          <!-- KPI Cards -->
          <div class="row">
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-xs-12">
              <div class="card">
                <div class="card-statistic-4">
                  <div class="align-items-center justify-content-between">
                    <div class="row">
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pr-0 pt-3">
                        <div class="card-content">
                          <h5 class="font-15">Total Properties</h5>
                          <h2 class="mb-3 font-18">{{ kpis.properties_total }}</h2>
                          <p class="mb-0"><span class="col-blue">Properties</span> Managed</p>
                        </div>
                      </div>
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pl-0">
                        <div class="banner-img">
                          <i class="fas fa-building fa-3x text-primary"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-xs-12">
              <div class="card">
                <div class="card-statistic-4">
                  <div class="align-items-center justify-content-between">
                    <div class="row">
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pr-0 pt-3">
                        <div class="card-content">
                          <h5 class="font-15">Total Units</h5>
                          <h2 class="mb-3 font-18">{{ kpis.units_total }}</h2>
                          <p class="mb-0"><span class="col-info">Units</span> Available</p>
                        </div>
                      </div>
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pl-0">
                        <div class="banner-img">
                          <i class="fas fa-door-open fa-3x text-info"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-xs-12">
              <div class="card">
                <div class="card-statistic-4">
                  <div class="align-items-center justify-content-between">
                    <div class="row">
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pr-0 pt-3">
                        <div class="card-content">
                          <h5 class="font-15">Active Leases</h5>
                          <h2 class="mb-3 font-18">{{ kpis.leases_active }}</h2>
                          <p class="mb-0"><span class="col-green">Leases</span> Active</p>
                        </div>
                      </div>
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pl-0">
                        <div class="banner-img">
                          <i class="fas fa-file-contract fa-3x text-success"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-xs-12">
              <div class="card">
                <div class="card-statistic-4">
                  <div class="align-items-center justify-content-between">
                    <div class="row">
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pr-0 pt-3">
                        <div class="card-content">
                          <h5 class="font-15">Unpaid Invoices</h5>
                          <h2 class="mb-3 font-18">{{ kpis.invoices_unpaid }}</h2>
                          <p class="mb-0"><span class="col-warning">Invoices</span> Pending</p>
                        </div>
                      </div>
                      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pl-0">
                        <div class="banner-img">
                          <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Latest Invoices -->
          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-header">
                  <h4>Latest Invoices</h4>
                  <div class="card-header-action">
                    <a href="{% org_url 'invoice_list' %}" class="btn btn-primary">View All</a>
                  </div>
                </div>
                <div class="card-body p-0">
                  <div class="table-responsive">
                    <table class="table table-striped mb-0">
                      <thead>
                        <tr>
                          <th>#</th>
                          <th>Lease</th>
                          <th>Issue Date</th>
                          <th>Due Date</th>
                          <th>Amount</th>
                          <th>Paid</th>
                          <th>Balance</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for inv in latest_invoices %}
                        <tr>
                          <td><a href="{% org_url 'invoice_detail' pk=inv.pk %}">{{ inv.number }}</a></td>
                          <td>{{ inv.lease.tenant }} — {{ inv.lease.unit.code }}</td>
                          <td>{{ inv.issue_date }}</td>
                          <td>{{ inv.due_date }}</td>
                          <td>${{ inv.amount_due }}</td>
                          <td>${{ inv.amount_paid }}</td>
                          <td>${{ inv.balance }}</td>
                          <td>
                            {% if inv.is_paid %}
                              <span class="badge badge-success">Paid</span>
                            {% else %}
                              <span class="badge badge-warning">Unpaid</span>
                            {% endif %}
                          </td>
                        </tr>
                        {% empty %}
                        <tr>
                          <td colspan="8" class="text-center">No invoices yet.</td>
                        </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <footer class="main-footer">
        <div class="footer-left">
          <a href="squaretech.co.ke">Insquare Technologies</a>
        </div>
        <div class="footer-right">
        </div>
      </footer>
    </div>
  </div>
  <!-- General JS Scripts -->
  <script src="{% static 'assets/js/app.min.js' %}"></script>
  <!-- JS Libraries -->
  <script src="{% static 'assets/bundles/apexcharts/apexcharts.min.js' %}"></script>
  <!-- Page Specific JS File -->
  <script src="{% static 'assets/js/page/index.js' %}"></script>
  <!-- Template JS File -->
  <script src="{% static 'assets/js/scripts.js' %}"></script>
  <!-- Custom JS File -->
  <script src="{% static 'assets/js/custom.js' %}"></script>
</body>
{% endblock %}
