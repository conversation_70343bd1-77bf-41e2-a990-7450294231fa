{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Leases{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">Leases</h1>
    <a href="{% org_url 'lease_create' %}" class="btn btn-sm btn-primary">Add Lease</a>
</div>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Unit</th>
                <th>Tenant</th>
                <th>Start</th>
                <th>End</th>
                <th>Rent</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        {% for l in items %}
        <tr>
            <td>{{ l.unit.property.name }} - {{ l.unit.code }}</td>
            <td>{{ l.tenant }}</td>
            <td>{{ l.start_date }}</td>
            <td>{{ l.end_date|default:'—' }}</td>
            <td>{{ l.rent_amount }}</td>
            <td><span class="badge bg-{% if l.status == 'ACTIVE' %}success{% elif l.status == 'PENDING' %}warning{% else %}secondary{% endif %}">{{ l.get_status_display }}</span></td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="{% org_url 'lease_edit' pk=l.pk %}" class="btn btn-outline-primary">Edit</a>
                    <a href="{% org_url 'lease_delete' pk=l.pk %}" class="btn btn-outline-danger">Delete</a>
                </div>
            </td>
        </tr>
        {% empty %}
            <tr>
                <td colspan="7" class="text-center">No leases yet.</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    {% endblock %}