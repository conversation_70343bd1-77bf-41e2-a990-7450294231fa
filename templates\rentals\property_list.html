{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Properties{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
<h1 class="h4 mb-0">Properties</h1>
<a href="{% org_url 'property_create' %}" class="btn btn-sm btn-primary">Add Property</a>
</div>
<div class="card">
<div class="card-body p-0">
<table class="table table-hover mb-0">
<thead><tr><th>Name</th><th>Address</th><th>Units</th><th>Actions</th></tr></thead>
<tbody>
{% for p in items %}
<tr>
<td>{{ p.name }}</td>
<td>{{ p.address }}</td>
<td>{{ p.unit_set.count }}</td>
<td>
    <div class="btn-group btn-group-sm">
        <a href="{% org_url 'property_edit' pk=p.pk %}" class="btn btn-outline-primary">Edit</a>
        <a href="{% org_url 'property_delete' pk=p.pk %}" class="btn btn-outline-danger">Delete</a>
    </div>
</td>
</tr>
{% empty %}
<tr><td colspan="4" class="text-center">No properties yet.</td></tr>
{% endfor %}
</tbody>
</table>
</div>
</div>
{% endblock %}