{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">{{ title }}</h1>
    <a href="{% org_url 'tenant_list' %}" class="btn btn-secondary">Back to Tenants</a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="text-danger">{{ form.first_name.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="text-danger">{{ form.last_name.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger">{{ form.email.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.phone.id_for_label }}" class="form-label">Phone</label>
                        {{ form.phone }}
                        {% if form.phone.errors %}
                            <div class="text-danger">{{ form.phone.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="{{ form.id_number.id_for_label }}" class="form-label">ID Number</label>
                {{ form.id_number }}
                {% if form.id_number.errors %}
                    <div class="text-danger">{{ form.id_number.errors }}</div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                {{ form.notes }}
                {% if form.notes.errors %}
                    <div class="text-danger">{{ form.notes.errors }}</div>
                {% endif %}
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Save Tenant</button>
                <a href="{% org_url 'tenant_list' %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
