{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Tenants{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">Tenants</h1>
    <a href="{% org_url 'tenant_create' %}" class="btn btn-sm btn-primary">Add Tenant</a>
</div>
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        {% for t in items %}
        <tr>
            <td>{{ t.first_name }} {{ t.last_name }}</td>
            <td>{{ t.email }}</td>
            <td>{{ t.phone }}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="{% org_url 'tenant_edit' pk=t.pk %}" class="btn btn-outline-primary">Edit</a>
                    <a href="{% org_url 'tenant_delete' pk=t.pk %}" class="btn btn-outline-danger">Delete</a>
                </div>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="text-center">No tenants yet.</td>
        </tr>
        {% endfor %}
        </tbody>
    </table>
    {% endblock %}