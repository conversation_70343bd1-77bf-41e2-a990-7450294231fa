{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">{{ title }}</h1>
    <a href="{% org_url 'unit_list' %}" class="btn btn-secondary">Back to Units</a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.property.id_for_label }}" class="form-label">Property</label>
                {{ form.property }}
                {% if form.property.errors %}
                    <div class="text-danger">{{ form.property.errors }}</div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.code.id_for_label }}" class="form-label">Unit Code</label>
                {{ form.code }}
                {% if form.code.errors %}
                    <div class="text-danger">{{ form.code.errors }}</div>
                {% endif %}
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.bedrooms.id_for_label }}" class="form-label">Bedrooms</label>
                        {{ form.bedrooms }}
                        {% if form.bedrooms.errors %}
                            <div class="text-danger">{{ form.bedrooms.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.bathrooms.id_for_label }}" class="form-label">Bathrooms</label>
                        {{ form.bathrooms }}
                        {% if form.bathrooms.errors %}
                            <div class="text-danger">{{ form.bathrooms.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="mb-3 form-check">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="form-check-label">Active</label>
                {% if form.is_active.errors %}
                    <div class="text-danger">{{ form.is_active.errors }}</div>
                {% endif %}
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Save Unit</button>
                <a href="{% org_url 'unit_list' %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
